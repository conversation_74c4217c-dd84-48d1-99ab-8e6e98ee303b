<?php
$pageTitle = 'العروض الخاصة';
require_once 'includes/header.php';

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$sortBy = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'discount_high';

// بناء استعلام البحث للمنتجات المخفضة
$whereConditions = ["p.status = 'active'", "p.discount > 0"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($category > 0) {
    $whereConditions[] = "p.category_id = ?";
    $params[] = $category;
}

$whereClause = implode(' AND ', $whereConditions);

// ترتيب النتائج
$orderBy = "p.discount DESC, p.created_at DESC";
switch ($sortBy) {
    case 'discount_low':
        $orderBy = "p.discount ASC";
        break;
    case 'price_low':
        $orderBy = "p.price ASC";
        break;
    case 'price_high':
        $orderBy = "p.price DESC";
        break;
    case 'name':
        $orderBy = "p.name ASC";
        break;
    case 'newest':
        $orderBy = "p.created_at DESC";
        break;
}

// التصفح (Pagination)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 12;
$offset = ($page - 1) * $perPage;

// عدد المنتجات الإجمالي
$countSql = "SELECT COUNT(*) as total FROM products p WHERE {$whereClause}";
$totalResult = fetchOne($countSql, $params);
$totalProducts = ($totalResult && isset($totalResult['total'])) ? $totalResult['total'] : 0;
$totalPages = ceil($totalProducts / $perPage);

// جلب المنتجات المخفضة
$sql = "
    SELECT p.*, c.name as category_name
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    WHERE {$whereClause}
    ORDER BY {$orderBy}
    LIMIT {$perPage} OFFSET {$offset}
";

$products = fetchAll($sql, $params);
// fetchAll now returns [] instead of false, so no need to check

// جلب التصنيفات للفلتر
$categories = fetchAll("SELECT id, name FROM categories ORDER BY name");
// fetchAll now returns [] instead of false, so no need to check

// حساب إجمالي الوفورات
$totalSavings = 0;
foreach ($products as $product) {
    $discount = $product['price'] * $product['discount'] / 100;
    $totalSavings += $discount;
}
?>

<!-- Page Header -->
<div class="bg-gradient-danger text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-percent"></i> العروض الخاصة
                </h1>
                <p class="lead mb-4">
                    اكتشف أفضل العروض والخصومات الحصرية على مجموعة واسعة من المنتجات المميزة
                </p>
                <div class="d-flex gap-4">
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $totalProducts; ?></div>
                        <div>منتج مخفض</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold">حتى 70%</div>
                        <div>خصم</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo formatPrice($totalSavings); ?></div>
                        <div>إجمالي الوفورات</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="bi bi-gift display-1"></i>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb -->
<div class="bg-light py-3">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">العروض الخاصة</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col-lg-3 mb-4">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="bi bi-funnel"></i> فلترة العروض</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="">
                        <!-- البحث -->
                        <div class="mb-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="ابحث في العروض...">
                        </div>
                        
                        <!-- التصنيف -->
                        <div class="mb-3">
                            <label class="form-label">التصنيف</label>
                            <select class="form-select" name="category">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo $cat['id']; ?>" 
                                            <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-search"></i> تطبيق الفلتر
                            </button>
                            <a href="<?php echo SITE_URL; ?>/offers.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Offer Categories -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">تصنيفات العروض</h6>
                </div>
                <div class="card-body">
                    <a href="<?php echo SITE_URL; ?>/offers.php" 
                       class="d-block text-decoration-none mb-2 <?php echo empty($category) ? 'text-danger fw-bold' : 'text-muted'; ?>">
                        <i class="bi bi-percent"></i> جميع العروض
                    </a>
                    <?php foreach ($categories as $cat): ?>
                        <?php
                        // عدد المنتجات المخفضة في كل تصنيف
                        $categoryCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE category_id = ? AND discount > 0 AND status = 'active'", [$cat['id']]);
                        if ($categoryCount && isset($categoryCount['count']) && $categoryCount['count'] > 0):
                        ?>
                            <a href="<?php echo SITE_URL; ?>/offers.php?category=<?php echo $cat['id']; ?>"
                               class="d-flex justify-content-between text-decoration-none mb-2 <?php echo $category == $cat['id'] ? 'text-danger fw-bold' : 'text-muted'; ?>">
                                <span><i class="bi bi-tag"></i> <?php echo htmlspecialchars($cat['name']); ?></span>
                                <span class="badge bg-danger"><?php echo ($categoryCount && isset($categoryCount['count'])) ? $categoryCount['count'] : 0; ?></span>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Special Offers Info -->
            <div class="card mt-4 border-warning">
                <div class="card-header bg-warning">
                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> معلومات العروض</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <i class="bi bi-clock text-warning"></i>
                        <strong>عروض محدودة الوقت</strong>
                        <p class="small text-muted mb-0">استغل الفرصة قبل انتهاء العرض</p>
                    </div>
                    <div class="mb-3">
                        <i class="bi bi-truck text-success"></i>
                        <strong>توصيل مجاني</strong>
                        <p class="small text-muted mb-0">للطلبات أكثر من <?php echo formatPrice(getSetting('free_delivery_threshold')); ?></p>
                    </div>
                    <div>
                        <i class="bi bi-shield-check text-primary"></i>
                        <strong>ضمان الجودة</strong>
                        <p class="small text-muted mb-0">جميع المنتجات أصلية ومضمونة</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col-lg-9">
            <!-- Sort Options -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <span class="text-muted">عرض <?php echo count($products); ?> من أصل <?php echo $totalProducts; ?> عرض</span>
                </div>
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">ترتيب حسب:</label>
                    <select class="form-select" style="width: auto;" onchange="changeSorting(this.value)">
                        <option value="discount_high" <?php echo $sortBy == 'discount_high' ? 'selected' : ''; ?>>أعلى خصم</option>
                        <option value="discount_low" <?php echo $sortBy == 'discount_low' ? 'selected' : ''; ?>>أقل خصم</option>
                        <option value="price_low" <?php echo $sortBy == 'price_low' ? 'selected' : ''; ?>>السعر: من الأقل للأعلى</option>
                        <option value="price_high" <?php echo $sortBy == 'price_high' ? 'selected' : ''; ?>>السعر: من الأعلى للأقل</option>
                        <option value="newest" <?php echo $sortBy == 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                        <option value="name" <?php echo $sortBy == 'name' ? 'selected' : ''; ?>>الاسم</option>
                    </select>
                </div>
            </div>
            
            <?php if ($products): ?>
                <div class="row">
                    <?php foreach ($products as $product): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card product-card h-100 position-relative border-danger">
                                <span class="discount-badge bg-danger">خصم <?php echo $product['discount']; ?>%</span>
                                
                                <div class="position-relative">
                                    <img src="<?php echo $product['image'] ? UPLOAD_URL . '/' . $product['image'] : 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=صورة+المنتج'; ?>" 
                                         class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    
                                    <!-- Savings Badge -->
                                    <?php 
                                    $originalPrice = $product['price'];
                                    $discountedPrice = $originalPrice - ($originalPrice * $product['discount'] / 100);
                                    $savings = $originalPrice - $discountedPrice;
                                    ?>
                                    <span class="badge bg-success position-absolute" style="top: 10px; left: 10px;">
                                        وفر <?php echo formatPrice($savings); ?>
                                    </span>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                                    </p>
                                    <p class="card-text text-muted flex-grow-1">
                                        <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                                    </p>
                                    
                                    <div class="price-section mb-3">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <span class="h5 text-danger mb-0"><?php echo formatPrice($discountedPrice); ?></span>
                                                <span class="text-muted text-decoration-line-through ms-2"><?php echo formatPrice($originalPrice); ?></span>
                                            </div>
                                            <span class="badge bg-danger fs-6"><?php echo $product['discount']; ?>% خصم</span>
                                        </div>
                                        <div class="text-success small mt-1">
                                            <i class="bi bi-piggy-bank"></i> توفر <?php echo formatPrice($savings); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <?php if ($product['stock'] > 0): ?>
                                            <button class="btn btn-danger flex-grow-1" onclick="addToCart(<?php echo $product['id']; ?>)">
                                                <i class="bi bi-cart-plus"></i> أضف للسلة
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-secondary flex-grow-1" disabled>
                                                <i class="bi bi-x-circle"></i> غير متوفر
                                            </button>
                                        <?php endif; ?>
                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                           class="btn btn-outline-danger">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                    
                                    <?php if ($product['stock'] <= 5 && $product['stock'] > 0): ?>
                                        <small class="text-warning mt-2">
                                            <i class="bi bi-exclamation-triangle"></i> 
                                            متبقي <?php echo $product['stock']; ?> قطع فقط - أسرع!
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-percent display-1 text-muted"></i>
                    <h3 class="mt-3">لا توجد عروض حالياً</h3>
                    <p class="text-muted">لم نجد أي عروض تطابق معايير البحث الخاصة بك</p>
                    <a href="<?php echo SITE_URL; ?>/offers.php" class="btn btn-danger">
                        عرض جميع العروض
                    </a>
                </div>
            <?php endif; ?>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="تصفح العروض" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildOffersPaginationUrl($page - 1); ?>">
                                    <i class="bi bi-chevron-right"></i> السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        if ($startPage > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildOffersPaginationUrl(1); ?>">1</a>
                            </li>
                            <?php if ($startPage > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="<?php echo buildOffersPaginationUrl($i); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($endPage < $totalPages): ?>
                            <?php if ($endPage < $totalPages - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildOffersPaginationUrl($totalPages); ?>"><?php echo $totalPages; ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildOffersPaginationUrl($page + 1); ?>">
                                    التالي <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body text-center py-5">
                    <h3 class="fw-bold mb-3">لا تفوت هذه الفرصة الذهبية!</h3>
                    <p class="lead mb-4">
                        اشترك في النشرة البريدية لتكون أول من يعلم بالعروض الجديدة والخصومات الحصرية
                    </p>
                    <form class="d-flex justify-content-center gap-2" style="max-width: 400px; margin: 0 auto;">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني" required>
                        <button type="submit" class="btn btn-light">
                            <i class="bi bi-send"></i> اشترك
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// دالة بناء رابط التصفح للعروض
function buildOffersPaginationUrl($pageNum) {
    $params = $_GET;
    $params['page'] = $pageNum;
    return SITE_URL . '/offers.php?' . http_build_query($params);
}
?>

<style>
.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(220, 53, 69, 0.2);
}

.discount-badge {
    font-size: 0.9rem;
    font-weight: 700;
    padding: 8px 12px;
    border-radius: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.card-img-top {
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1rem;
    }
}
</style>

<script>
// تغيير الترتيب
function changeSorting(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    url.searchParams.delete('page');
    window.location.href = url.toString();
}

// تحديث الفلاتر تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const inputs = filterForm.querySelectorAll('input, select');

    inputs.forEach(input => {
        if (input.name !== 'search') {
            input.addEventListener('change', function() {
                setTimeout(() => {
                    filterForm.submit();
                }, 300);
            });
        }
    });

    // البحث المباشر
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 3 || this.value.length === 0) {
                filterForm.submit();
            }
        }, 500);
    });
});

// تأثيرات بصرية للمنتجات
document.addEventListener('DOMContentLoaded', function() {
    const productCards = document.querySelectorAll('.product-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    productCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(card);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
