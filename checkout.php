<?php
$pageTitle = 'إتمام الطلب';
require_once 'includes/header.php';

// التحقق من وجود منتجات في السلة
$cart = getCart();
if (empty($cart)) {
    header('Location: ' . SITE_URL . '/cart.php');
    exit();
}

// جلب تفاصيل المنتجات
$productIds = array_keys($cart);
$placeholders = str_repeat('?,', count($productIds) - 1) . '?';

$products = fetchAll("
    SELECT id, name, price, discount, image, stock 
    FROM products 
    WHERE id IN ($placeholders) AND status = 'active'
", $productIds);

$cartItems = [];
$subtotal = 0;

foreach ($products as $product) {
    $quantity = $cart[$product['id']];
    $price = $product['price'];
    
    // تطبيق الخصم إن وجد
    if ($product['discount'] > 0) {
        $price = $price - ($price * $product['discount'] / 100);
    }
    
    $total = $price * $quantity;
    $subtotal += $total;
    
    $cartItems[] = [
        'product' => $product,
        'quantity' => $quantity,
        'price' => $price,
        'total' => $total
    ];
}

// حساب تكلفة التوصيل
$deliveryPrice = (float)getSetting('delivery_price');
$freeDeliveryThreshold = (float)getSetting('free_delivery_threshold');

if ($subtotal >= $freeDeliveryThreshold) {
    $deliveryPrice = 0;
}

// استرداد كود الخصم المطبق
$discountAmount = 0;
$discountCode = '';
if (isset($_SESSION['applied_discount'])) {
    $discountAmount = $_SESSION['applied_discount']['amount'];
    $discountCode = $_SESSION['applied_discount']['code'];
}

$finalTotal = $subtotal - $discountAmount + $deliveryPrice;

// معالجة إرسال الطلب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['place_order'])) {
    $customerName = sanitizeInput($_POST['customer_name']);
    $customerPhone = sanitizeInput($_POST['customer_phone']);
    $customerEmail = sanitizeInput($_POST['customer_email']);
    $address = sanitizeInput($_POST['address']);
    $province = sanitizeInput($_POST['province']);
    $city = sanitizeInput($_POST['city']);
    $postalCode = sanitizeInput($_POST['postal_code']);
    $paymentMethod = sanitizeInput($_POST['payment_method']);
    $notes = sanitizeInput($_POST['notes']);
    
    $errors = [];
    
    // التحقق من البيانات المطلوبة
    if (empty($customerName)) $errors[] = 'الاسم مطلوب';
    if (empty($customerPhone)) $errors[] = 'رقم الهاتف مطلوب';
    if (empty($address)) $errors[] = 'العنوان مطلوب';
    if (empty($province)) $errors[] = 'المحافظة مطلوبة';
    if (!empty($customerEmail) && !validateEmail($customerEmail)) $errors[] = 'البريد الإلكتروني غير صحيح';
    
    if (empty($errors)) {
        try {
            // بدء المعاملة
            $pdo->beginTransaction();
            
            // إدراج الطلب
            $orderData = [
                'customer_name' => $customerName,
                'customer_phone' => $customerPhone,
                'customer_email' => $customerEmail,
                'address' => $address,
                'province' => $province,
                'city' => $city,
                'postal_code' => $postalCode,
                'subtotal' => $subtotal,
                'delivery_price' => $deliveryPrice,
                'discount_amount' => $discountAmount,
                'total_price' => $finalTotal,
                'payment_method' => $paymentMethod,
                'notes' => $notes,
                'status' => 'pending'
            ];
            
            $orderId = insertData('orders', $orderData);
            
            if (!$orderId) {
                throw new Exception('فشل في إنشاء الطلب');
            }
            
            // إدراج عناصر الطلب
            foreach ($cartItems as $item) {
                $orderItemData = [
                    'order_id' => $orderId,
                    'product_id' => $item['product']['id'],
                    'product_name' => $item['product']['name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'total' => $item['total']
                ];
                
                if (!insertData('order_items', $orderItemData)) {
                    throw new Exception('فشل في إضافة عناصر الطلب');
                }
                
                // تحديث المخزن
                $newStock = $item['product']['stock'] - $item['quantity'];
                updateData('products', ['stock' => $newStock], 'id = ?', [$item['product']['id']]);
            }
            
            // تحديث استخدام كود الخصم
            if (!empty($discountCode)) {
                $pdo->prepare("UPDATE discount_codes SET used_count = used_count + 1 WHERE code = ?")->execute([$discountCode]);
            }
            
            // تأكيد المعاملة
            $pdo->commit();
            
            // إفراغ السلة وكود الخصم
            clearCart();
            unset($_SESSION['applied_discount']);
            
            // إعادة توجيه إلى صفحة تأكيد الطلب
            header('Location: ' . SITE_URL . '/order-success.php?order=' . $orderId);
            exit();
            
        } catch (Exception $e) {
            // إلغاء المعاملة
            $pdo->rollBack();
            
            $_SESSION['message'] = [
                'text' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage(),
                'type' => 'error'
            ];
        }
    } else {
        $_SESSION['message'] = [
            'text' => implode('<br>', $errors),
            'type' => 'error'
        ];
    }
}

// قائمة المحافظات
$provinces = [
    'الرياض', 'مكة المكرمة', 'المدينة المنورة', 'القصيم', 'المنطقة الشرقية',
    'عسير', 'تبوك', 'حائل', 'الحدود الشمالية', 'جازان', 'نجران', 'الباحة', 'الجوف'
];
?>

<!-- Breadcrumb -->
<div class="bg-light py-3">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/cart.php">سلة التسوق</a></li>
                <li class="breadcrumb-item active">إتمام الطلب</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <!-- Checkout Form -->
        <div class="col-lg-8 mb-4">
            <form method="POST" action="" id="checkoutForm">
                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person"></i> معلومات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="customer_name" 
                                       value="<?php echo isset($_POST['customer_name']) ? htmlspecialchars($_POST['customer_name']) : ''; ?>" 
                                       required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" name="customer_phone" 
                                       value="<?php echo isset($_POST['customer_phone']) ? htmlspecialchars($_POST['customer_phone']) : ''; ?>" 
                                       required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="customer_email" 
                                   value="<?php echo isset($_POST['customer_email']) ? htmlspecialchars($_POST['customer_email']) : ''; ?>">
                            <div class="form-text">اختياري - لإرسال تحديثات الطلب</div>
                        </div>
                    </div>
                </div>
                
                <!-- Shipping Address -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-geo-alt"></i> عنوان التوصيل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">العنوان التفصيلي <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="address" rows="3" 
                                      placeholder="الحي، الشارع، رقم المبنى، رقم الشقة..." 
                                      required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المحافظة <span class="text-danger">*</span></label>
                                <select class="form-select" name="province" required>
                                    <option value="">اختر المحافظة</option>
                                    <?php foreach ($provinces as $prov): ?>
                                        <option value="<?php echo $prov; ?>" 
                                                <?php echo (isset($_POST['province']) && $_POST['province'] == $prov) ? 'selected' : ''; ?>>
                                            <?php echo $prov; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" name="city" 
                                       value="<?php echo isset($_POST['city']) ? htmlspecialchars($_POST['city']) : ''; ?>">
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الرمز البريدي</label>
                            <input type="text" class="form-control" name="postal_code" 
                                   value="<?php echo isset($_POST['postal_code']) ? htmlspecialchars($_POST['postal_code']) : ''; ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Payment Method -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-credit-card"></i> طريقة الدفع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="payment_method" 
                                   id="cash_on_delivery" value="cash_on_delivery" checked>
                            <label class="form-check-label" for="cash_on_delivery">
                                <i class="bi bi-cash text-success"></i>
                                <strong>الدفع عند الاستلام</strong>
                                <div class="small text-muted">ادفع نقداً عند وصول الطلب</div>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="payment_method" 
                                   id="bank_transfer" value="bank_transfer">
                            <label class="form-check-label" for="bank_transfer">
                                <i class="bi bi-bank text-primary"></i>
                                <strong>التحويل البنكي</strong>
                                <div class="small text-muted">سيتم إرسال تفاصيل التحويل بعد تأكيد الطلب</div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Order Notes -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-text"></i> ملاحظات إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="أي ملاحظات خاصة بالطلب أو التوصيل..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="d-grid">
                    <button type="submit" name="place_order" class="btn btn-primary btn-lg">
                        <i class="bi bi-check-circle"></i> تأكيد الطلب
                    </button>
                </div>
            </form>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    <!-- Order Items -->
                    <div class="order-items mb-4">
                        <?php foreach ($cartItems as $item): ?>
                            <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                <img src="<?php echo $item['product']['image'] ? UPLOAD_URL . '/' . $item['product']['image'] : 'https://via.placeholder.com/60x60/f8f9fa/6c757d?text=صورة'; ?>"
                                     alt="<?php echo htmlspecialchars($item['product']['name']); ?>"
                                     class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($item['product']['name']); ?></h6>
                                    <div class="small text-muted">
                                        الكمية: <?php echo $item['quantity']; ?> × <?php echo formatPrice($item['price']); ?>
                                    </div>
                                    <div class="fw-bold text-primary">
                                        <?php echo formatPrice($item['total']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Order Totals -->
                    <div class="order-totals">
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span><?php echo formatPrice($subtotal); ?></span>
                        </div>

                        <?php if ($discountAmount > 0): ?>
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>الخصم (<?php echo htmlspecialchars($discountCode); ?>):</span>
                                <span>-<?php echo formatPrice($discountAmount); ?></span>
                            </div>
                        <?php endif; ?>

                        <div class="d-flex justify-content-between mb-2">
                            <span>التوصيل:</span>
                            <span>
                                <?php if ($deliveryPrice == 0): ?>
                                    <span class="text-success">مجاني</span>
                                <?php else: ?>
                                    <?php echo formatPrice($deliveryPrice); ?>
                                <?php endif; ?>
                            </span>
                        </div>

                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>الإجمالي:</strong>
                            <strong class="text-primary fs-5"><?php echo formatPrice($finalTotal); ?></strong>
                        </div>
                    </div>

                    <!-- Security Info -->
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-shield-check fs-4 me-2"></i>
                            <div>
                                <strong>عملية آمنة</strong>
                                <div class="small">معلوماتك محمية بأعلى معايير الأمان</div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="text-center">
                        <p class="small text-muted mb-2">هل تحتاج مساعدة؟</p>
                        <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>"
                           class="btn btn-success btn-sm" target="_blank">
                            <i class="bi bi-whatsapp"></i> تواصل معنا
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
document.getElementById('checkoutForm').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    // التحقق من رقم الهاتف
    const phoneField = this.querySelector('[name="customer_phone"]');
    const phonePattern = /^[0-9+\-\s()]+$/;
    if (phoneField.value && !phonePattern.test(phoneField.value)) {
        phoneField.classList.add('is-invalid');
        showToast('رقم الهاتف غير صحيح', 'error');
        isValid = false;
    }

    // التحقق من البريد الإلكتروني
    const emailField = this.querySelector('[name="customer_email"]');
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailField.value && !emailPattern.test(emailField.value)) {
        emailField.classList.add('is-invalid');
        showToast('البريد الإلكتروني غير صحيح', 'error');
        isValid = false;
    }

    if (!isValid) {
        e.preventDefault();
        showToast('يرجى تصحيح الأخطاء في النموذج', 'error');

        // التمرير إلى أول حقل خاطئ
        const firstInvalidField = this.querySelector('.is-invalid');
        if (firstInvalidField) {
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstInvalidField.focus();
        }
    } else {
        // إظهار مؤشر التحميل
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري معالجة الطلب...';
        submitButton.disabled = true;

        // إعادة تفعيل الزر في حالة فشل الإرسال
        setTimeout(() => {
            if (submitButton.disabled) {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        }, 10000);
    }
});

// إزالة رسائل الخطأ عند الكتابة
document.querySelectorAll('input, select, textarea').forEach(field => {
    field.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});

// تنسيق رقم الهاتف تلقائياً
document.querySelector('[name="customer_phone"]').addEventListener('input', function() {
    // إزالة جميع الأحرف غير الرقمية
    let value = this.value.replace(/\D/g, '');

    // تنسيق الرقم السعودي
    if (value.startsWith('966')) {
        value = '+' + value;
    } else if (value.startsWith('05')) {
        value = '+966' + value.substring(1);
    } else if (value.startsWith('5') && value.length === 9) {
        value = '+966' + value;
    }

    this.value = value;
});

// حفظ البيانات في التخزين المحلي
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('checkoutForm');
    const formData = JSON.parse(localStorage.getItem('checkoutFormData') || '{}');

    // استرداد البيانات المحفوظة
    Object.keys(formData).forEach(key => {
        const field = form.querySelector(`[name="${key}"]`);
        if (field && field.type !== 'radio') {
            field.value = formData[key];
        } else if (field && field.type === 'radio' && field.value === formData[key]) {
            field.checked = true;
        }
    });

    // حفظ البيانات عند التغيير
    form.addEventListener('input', function(e) {
        const field = e.target;
        if (field.name) {
            formData[field.name] = field.value;
            localStorage.setItem('checkoutFormData', JSON.stringify(formData));
        }
    });

    // مسح البيانات المحفوظة عند إرسال النموذج بنجاح
    form.addEventListener('submit', function() {
        setTimeout(() => {
            localStorage.removeItem('checkoutFormData');
        }, 1000);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
