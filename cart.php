<?php
$pageTitle = 'سلة التسوق';
require_once 'includes/header.php';

// جلب محتويات السلة
$cart = getCart();
$cartItems = [];
$subtotal = 0;

if (!empty($cart)) {
    $productIds = array_keys($cart);
    $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
    
    $products = fetchAll("
        SELECT id, name, price, discount, image, stock 
        FROM products 
        WHERE id IN ($placeholders) AND status = 'active'
    ", $productIds);
    
    foreach ($products as $product) {
        $quantity = $cart[$product['id']];
        $price = $product['price'];
        
        // تطبيق الخصم إن وجد
        if ($product['discount'] > 0) {
            $price = $price - ($price * $product['discount'] / 100);
        }
        
        $total = $price * $quantity;
        $subtotal += $total;
        
        $cartItems[] = [
            'product' => $product,
            'quantity' => $quantity,
            'price' => $price,
            'total' => $total
        ];
    }
}

// حساب تكلفة التوصيل
$deliveryPrice = (float)getSetting('delivery_price');
$freeDeliveryThreshold = (float)getSetting('free_delivery_threshold');

if ($subtotal >= $freeDeliveryThreshold) {
    $deliveryPrice = 0;
}

$total = $subtotal + $deliveryPrice;

// معالجة تطبيق كود الخصم
$discountAmount = 0;
$discountCode = '';
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['apply_discount'])) {
    $discountCode = sanitizeInput($_POST['discount_code']);
    
    if (!empty($discountCode)) {
        $discount = fetchOne("
            SELECT * FROM discount_codes 
            WHERE code = ? AND status = 'active' 
            AND (expiration_date IS NULL OR expiration_date >= CURDATE())
            AND (usage_limit IS NULL OR used_count < usage_limit)
        ", [$discountCode]);
        
        if ($discount) {
            if ($subtotal >= $discount['min_order_amount']) {
                if ($discount['type'] == 'percentage') {
                    $discountAmount = $subtotal * $discount['amount'] / 100;
                } else {
                    $discountAmount = $discount['amount'];
                }
                
                // التأكد من أن الخصم لا يتجاوز المبلغ الفرعي
                $discountAmount = min($discountAmount, $subtotal);
                
                $_SESSION['applied_discount'] = [
                    'code' => $discountCode,
                    'amount' => $discountAmount
                ];
                
                $_SESSION['message'] = [
                    'text' => 'تم تطبيق كود الخصم بنجاح!',
                    'type' => 'success'
                ];
            } else {
                $_SESSION['message'] = [
                    'text' => 'الحد الأدنى للطلب لاستخدام هذا الكود هو ' . formatPrice($discount['min_order_amount']),
                    'type' => 'error'
                ];
            }
        } else {
            $_SESSION['message'] = [
                'text' => 'كود الخصم غير صحيح أو منتهي الصلاحية',
                'type' => 'error'
            ];
        }
        
        header('Location: ' . SITE_URL . '/cart.php');
        exit();
    }
}

// استرداد كود الخصم المطبق
if (isset($_SESSION['applied_discount'])) {
    $discountAmount = $_SESSION['applied_discount']['amount'];
    $discountCode = $_SESSION['applied_discount']['code'];
}

$finalTotal = $subtotal - $discountAmount + $deliveryPrice;
?>

<!-- Breadcrumb -->
<div class="bg-light py-3">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">سلة التسوق</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container my-5">
    <?php if (empty($cartItems)): ?>
        <!-- Empty Cart -->
        <div class="text-center py-5">
            <i class="bi bi-cart-x display-1 text-muted"></i>
            <h2 class="mt-4">سلة التسوق فارغة</h2>
            <p class="text-muted lead">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg mt-3">
                <i class="bi bi-grid"></i> تصفح المنتجات
            </a>
        </div>
    <?php else: ?>
        <div class="row">
            <!-- Cart Items -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="bi bi-cart3"></i> منتجات السلة (<?php echo count($cartItems); ?> منتج)
                        </h4>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الإجمالي</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cartItems as $item): ?>
                                        <tr data-product-id="<?php echo $item['product']['id']; ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo $item['product']['image'] ? UPLOAD_URL . '/' . $item['product']['image'] : 'https://via.placeholder.com/80x80/f8f9fa/6c757d?text=صورة'; ?>" 
                                                         alt="<?php echo htmlspecialchars($item['product']['name']); ?>" 
                                                         class="rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($item['product']['name']); ?></h6>
                                                        <?php if ($item['product']['discount'] > 0): ?>
                                                            <small class="text-success">
                                                                <i class="bi bi-percent"></i> خصم <?php echo $item['product']['discount']; ?>%
                                                            </small>
                                                        <?php endif; ?>
                                                        <div class="small text-muted">
                                                            متوفر: <?php echo $item['product']['stock']; ?> قطعة
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="align-middle">
                                                <div class="price-info">
                                                    <span class="fw-bold"><?php echo formatPrice($item['price']); ?></span>
                                                    <?php if ($item['product']['discount'] > 0): ?>
                                                        <div class="small text-muted text-decoration-line-through">
                                                            <?php echo formatPrice($item['product']['price']); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="align-middle">
                                                <div class="input-group" style="width: 120px;">
                                                    <button class="btn btn-outline-secondary btn-sm" type="button" 
                                                            onclick="updateQuantity(<?php echo $item['product']['id']; ?>, <?php echo $item['quantity'] - 1; ?>)">
                                                        <i class="bi bi-dash"></i>
                                                    </button>
                                                    <input type="number" class="form-control form-control-sm text-center" 
                                                           value="<?php echo $item['quantity']; ?>" 
                                                           min="1" max="<?php echo $item['product']['stock']; ?>"
                                                           onchange="updateQuantity(<?php echo $item['product']['id']; ?>, this.value)">
                                                    <button class="btn btn-outline-secondary btn-sm" type="button" 
                                                            onclick="updateQuantity(<?php echo $item['product']['id']; ?>, <?php echo $item['quantity'] + 1; ?>)">
                                                        <i class="bi bi-plus"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td class="align-middle">
                                                <span class="fw-bold item-total"><?php echo formatPrice($item['total']); ?></span>
                                            </td>
                                            <td class="align-middle">
                                                <button class="btn btn-outline-danger btn-sm" 
                                                        onclick="removeFromCart(<?php echo $item['product']['id']; ?>)"
                                                        title="حذف من السلة">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-right"></i> متابعة التسوق
                            </a>
                            <button class="btn btn-outline-danger" onclick="clearCart()">
                                <i class="bi bi-trash"></i> إفراغ السلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card sticky-top" style="top: 20px;">
                    <div class="card-header">
                        <h5 class="mb-0">ملخص الطلب</h5>
                    </div>
                    <div class="card-body">
                        <!-- Discount Code -->
                        <form method="POST" class="mb-4">
                            <label class="form-label">كود الخصم</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="discount_code" 
                                       placeholder="أدخل كود الخصم" value="<?php echo htmlspecialchars($discountCode); ?>">
                                <button type="submit" name="apply_discount" class="btn btn-outline-primary">
                                    تطبيق
                                </button>
                            </div>
                        </form>
                        
                        <!-- Order Details -->
                        <div class="order-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal"><?php echo formatPrice($subtotal); ?></span>
                            </div>
                            
                            <?php if ($discountAmount > 0): ?>
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>الخصم (<?php echo htmlspecialchars($discountCode); ?>):</span>
                                    <span>-<?php echo formatPrice($discountAmount); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>التوصيل:</span>
                                <span>
                                    <?php if ($deliveryPrice == 0): ?>
                                        <span class="text-success">مجاني</span>
                                    <?php else: ?>
                                        <?php echo formatPrice($deliveryPrice); ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <?php if ($subtotal < $freeDeliveryThreshold && $deliveryPrice > 0): ?>
                                <div class="alert alert-info small">
                                    <i class="bi bi-info-circle"></i>
                                    أضف <?php echo formatPrice($freeDeliveryThreshold - $subtotal); ?> للحصول على توصيل مجاني
                                </div>
                            <?php endif; ?>
                            
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>الإجمالي:</strong>
                                <strong class="text-primary fs-5" id="final-total"><?php echo formatPrice($finalTotal); ?></strong>
                            </div>
                        </div>
                        
                        <a href="<?php echo SITE_URL; ?>/checkout.php" class="btn btn-primary w-100 btn-lg">
                            <i class="bi bi-credit-card"></i> إتمام الطلب
                        </a>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="bi bi-shield-check"></i> عملية دفع آمنة ومضمونة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// تحديث كمية المنتج
function updateQuantity(productId, quantity) {
    quantity = parseInt(quantity);

    if (quantity < 1) {
        removeFromCart(productId);
        return;
    }

    const formData = new FormData();
    formData.append('action', 'update');
    formData.append('product_id', productId);
    formData.append('quantity', quantity);

    fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartDisplay();
            showToast('تم تحديث الكمية بنجاح', 'success');
        } else {
            showToast(data.message || 'حدث خطأ أثناء تحديث الكمية', 'error');
            // إعادة تحميل الصفحة في حالة الخطأ
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
}

// حذف منتج من السلة
function removeFromCart(productId) {
    if (!confirm('هل أنت متأكد من حذف هذا المنتج من السلة؟')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'remove');
    formData.append('product_id', productId);

    fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة الصف من الجدول
            const row = document.querySelector(`tr[data-product-id="${productId}"]`);
            if (row) {
                row.remove();
            }

            updateCartDisplay();
            updateCartCount();
            showToast('تم حذف المنتج من السلة', 'success');

            // إعادة تحميل الصفحة إذا كانت السلة فارغة
            if (data.cart_count === 0) {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        } else {
            showToast(data.message || 'حدث خطأ أثناء حذف المنتج', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
}

// إفراغ السلة
function clearCart() {
    if (!confirm('هل أنت متأكد من إفراغ السلة بالكامل؟')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'clear');
    formData.append('product_id', 1); // قيمة وهمية مطلوبة

    fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم إفراغ السلة بنجاح', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast(data.message || 'حدث خطأ أثناء إفراغ السلة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
}

// تحديث عرض السلة
function updateCartDisplay() {
    // إعادة حساب الإجماليات
    let subtotal = 0;

    document.querySelectorAll('tr[data-product-id]').forEach(row => {
        const quantityInput = row.querySelector('input[type="number"]');
        const priceElement = row.querySelector('.price-info .fw-bold');
        const totalElement = row.querySelector('.item-total');

        if (quantityInput && priceElement && totalElement) {
            const quantity = parseInt(quantityInput.value);
            const priceText = priceElement.textContent.replace(/[^\d.]/g, '');
            const price = parseFloat(priceText);
            const itemTotal = price * quantity;

            totalElement.textContent = formatPrice(itemTotal);
            subtotal += itemTotal;
        }
    });

    // تحديث المجموع الفرعي
    const subtotalElement = document.getElementById('subtotal');
    if (subtotalElement) {
        subtotalElement.textContent = formatPrice(subtotal);
    }

    // إعادة حساب الإجمالي النهائي
    const deliveryPrice = <?php echo $deliveryPrice; ?>;
    const discountAmount = <?php echo $discountAmount; ?>;
    const finalTotal = subtotal - discountAmount + deliveryPrice;

    const finalTotalElement = document.getElementById('final-total');
    if (finalTotalElement) {
        finalTotalElement.textContent = formatPrice(finalTotal);
    }
}

// تنسيق السعر (دالة مساعدة) - محدثة لعرض أرقام صحيحة
function formatPrice(price) {
    // تحويل إلى رقم صحيح
    const wholePrice = Math.round(price);
    return new Intl.NumberFormat('ar-SA', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(wholePrice) + ' دينار عراقي';
}

// تحديث أزرار الكمية عند تغيير المدخل
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('change', function() {
            const productId = this.closest('tr').dataset.productId;
            const quantity = parseInt(this.value);
            const maxQuantity = parseInt(this.max);

            if (quantity > maxQuantity) {
                this.value = maxQuantity;
                showToast(`الحد الأقصى المتاح هو ${maxQuantity} قطع`, 'warning');
                return;
            }

            if (quantity < 1) {
                this.value = 1;
                return;
            }

            updateQuantity(productId, quantity);
        });
    });
});

// تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير hover على صفوف الجدول
    document.querySelectorAll('tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // تأثير على أزرار الكمية
    document.querySelectorAll('.input-group button').forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 100);
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
