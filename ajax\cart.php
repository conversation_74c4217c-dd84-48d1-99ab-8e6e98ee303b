<?php
require_once '../config/config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

$action = isset($_POST['action']) ? $_POST['action'] : 'add';
$productId = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

if ($productId <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج غير صحيح']);
    exit();
}

// التحقق من وجود المنتج
$product = fetchOne("SELECT id, name, price, stock, discount FROM products WHERE id = ? AND status = 'active'", [$productId]);

if (!$product) {
    echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
    exit();
}

// التحقق من توفر الكمية
if ($product['stock'] < $quantity) {
    echo json_encode(['success' => false, 'message' => 'الكمية المطلوبة غير متوفرة في المخزن']);
    exit();
}

switch ($action) {
    case 'add':
        // إضافة المنتج إلى السلة
        $cart = getCart();
        $currentQuantity = isset($cart[$productId]) ? $cart[$productId] : 0;
        $newQuantity = $currentQuantity + $quantity;
        
        // التحقق من الكمية الإجمالية
        if ($newQuantity > $product['stock']) {
            echo json_encode(['success' => false, 'message' => 'لا يمكن إضافة هذه الكمية. المتوفر في المخزن: ' . $product['stock']]);
            exit();
        }
        
        addToCart($productId, $quantity);
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم إضافة المنتج إلى السلة بنجاح',
            'cart_count' => getCartItemCount(),
            'product_name' => $product['name']
        ]);
        break;
        
    case 'update':
        // تحديث كمية المنتج في السلة
        if ($quantity > $product['stock']) {
            echo json_encode(['success' => false, 'message' => 'الكمية المطلوبة غير متوفرة في المخزن']);
            exit();
        }
        
        updateCartItem($productId, $quantity);
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم تحديث الكمية بنجاح',
            'cart_count' => getCartItemCount(),
            'cart_total' => getCartTotal()
        ]);
        break;
        
    case 'remove':
        // حذف المنتج من السلة
        removeFromCart($productId);
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم حذف المنتج من السلة',
            'cart_count' => getCartItemCount(),
            'cart_total' => getCartTotal()
        ]);
        break;
        
    case 'clear':
        // إفراغ السلة
        clearCart();
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم إفراغ السلة بنجاح',
            'cart_count' => 0,
            'cart_total' => 0
        ]);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'عملية غير صحيحة']);
        break;
}
?>
