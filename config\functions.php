<?php
/**
 * ملف الدوال المساعدة
 * Helper Functions File
 */

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * التأكد من أن النتيجة مصفوفة
 */
function ensureArray($data) {
    return is_array($data) ? $data : [];
}

/**
 * التحقق من صحة نتيجة دالة
 */
function isValidResult($result) {
    return $result !== false && $result !== null;
}

/**
 * تنسيق السعر - محدثة لعرض أرقام صحيحة بدون فواصل عشرية
 */
function formatPrice($price) {
    // تحويل السعر إلى رقم صحيح (إزالة الفواصل العشرية)
    $wholePrice = (int)round($price);
    // تنسيق الرقم مع فواصل الآلاف وعرض العملة العراقية
    return number_format($wholePrice, 0, '.', ',') . ' دينار عراقي';
}

/**
 * رفع الصور
 */
function uploadImage($file, $folder) {
    // التحقق من صحة البيانات المدخلة
    if (!is_array($file) || !isset($file['type']) || !isset($file['size']) || !isset($file['name']) || !isset($file['tmp_name'])) {
        return ['success' => false, 'error' => 'بيانات الملف غير صحيحة'];
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'error' => 'نوع الملف غير مدعوم. يُسمح بـ JPG, PNG, GIF فقط'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'error' => 'حجم الملف كبير جداً. الحد الأقصى 5MB'];
    }
    
    $uploadDir = UPLOAD_PATH . '/' . $folder . '/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $uploadPath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'error' => 'فشل في رفع الملف'];
    }
}

/**
 * إدراج بيانات في قاعدة البيانات
 */
function insertData($table, $data) {
    // الحصول على اتصال قاعدة البيانات
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        return false;
    }

    $columns = implode(',', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));

    $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";

    try {
        $stmt = $pdo->prepare($sql);

        foreach ($data as $key => $value) {
            $stmt->bindValue(":$key", $value);
        }

        if ($stmt->execute()) {
            return $pdo->lastInsertId();
        }

        return false;
    } catch (PDOException $e) {
        error_log("Insert Error: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث بيانات في قاعدة البيانات
 */
function updateData($table, $data, $where, $whereParams = []) {
    // الحصول على اتصال قاعدة البيانات
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        return false;
    }

    $setParts = [];
    foreach (array_keys($data) as $key) {
        $setParts[] = "$key = :$key";
    }
    $setClause = implode(', ', $setParts);

    $sql = "UPDATE $table SET $setClause WHERE $where";

    try {
        $stmt = $pdo->prepare($sql);

        foreach ($data as $key => $value) {
            $stmt->bindValue(":$key", $value);
        }

        // ربط معاملات WHERE
        if (!empty($whereParams)) {
            foreach ($whereParams as $key => $value) {
                $stmt->bindValue(":$key", $value);
            }
        }

        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Update Error: " . $e->getMessage());
        return false;
    }
}

/**
 * حذف بيانات من قاعدة البيانات
 */
function deleteData($table, $where, $params = []) {
    // الحصول على اتصال قاعدة البيانات
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        return false;
    }

    $sql = "DELETE FROM $table WHERE $where";

    try {
        $stmt = $pdo->prepare($sql);
        return $stmt->execute($params);
    } catch (PDOException $e) {
        error_log("Delete Error: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من تسجيل دخول المدير
 */
function requireAdminLogin() {
    if (!isset($_SESSION[ADMIN_SESSION_NAME]) || !$_SESSION[ADMIN_SESSION_NAME]) {
        header('Location: login.php');
        exit();
    }
}

/**
 * الحصول على معلومات المدير الحالي
 */
function getCurrentAdmin() {
    if (isset($_SESSION['admin_id'])) {
        return fetchOne("SELECT * FROM admins WHERE id = ?", [$_SESSION['admin_id']]);
    }
    return null;
}

/**
 * تسجيل خروج المدير
 */
function adminLogout() {
    unset($_SESSION[ADMIN_SESSION_NAME]);
    unset($_SESSION['admin_id']);
    session_destroy();
    header('Location: login.php');
    exit();
}

/**
 * حساب السعر بعد الخصم
 */
function calculateDiscountedPrice($price, $discount) {
    if ($discount > 0) {
        return $price - ($price * $discount / 100);
    }
    return $price;
}

/**
 * التحقق من صحة كود الخصم
 */
function validateDiscountCode($code, $orderTotal = 0) {
    $discountCode = fetchOne("
        SELECT * FROM discount_codes 
        WHERE code = ? AND status = 'active'
    ", [$code]);
    
    if (!$discountCode) {
        return ['valid' => false, 'error' => 'كود الخصم غير صحيح'];
    }
    
    // التحقق من تاريخ الانتهاء
    if ($discountCode['expiration_date'] && strtotime($discountCode['expiration_date']) < time()) {
        return ['valid' => false, 'error' => 'كود الخصم منتهي الصلاحية'];
    }
    
    // التحقق من حد الاستخدام
    if ($discountCode['usage_limit'] && $discountCode['used_count'] >= $discountCode['usage_limit']) {
        return ['valid' => false, 'error' => 'تم استنفاد استخدامات كود الخصم'];
    }
    
    // التحقق من الحد الأدنى للطلب
    if ($orderTotal < $discountCode['min_order_amount']) {
        return ['valid' => false, 'error' => 'الحد الأدنى للطلب ' . formatPrice($discountCode['min_order_amount'])];
    }
    
    // حساب قيمة الخصم
    $discountAmount = 0;
    if ($discountCode['type'] == 'percentage') {
        $discountAmount = $orderTotal * ($discountCode['amount'] / 100);
    } else {
        $discountAmount = $discountCode['amount'];
    }
    
    return [
        'valid' => true,
        'code' => $discountCode,
        'discount_amount' => $discountAmount
    ];
}

/**
 * حساب سعر التوصيل
 */
function calculateDeliveryPrice($orderTotal) {
    $deliveryPrice = getSetting('delivery_price', 25);
    $freeDeliveryThreshold = getSetting('free_delivery_threshold', 200);
    
    if ($orderTotal >= $freeDeliveryThreshold) {
        return 0;
    }
    
    return $deliveryPrice;
}

/**
 * الحصول على إعداد من قاعدة البيانات
 */
function getSetting($key, $default = '') {
    $setting = fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = ?", [$key]);
    return $setting ? $setting['setting_value'] : $default;
}

/**
 * تحديث إعداد في قاعدة البيانات
 */
function updateSetting($key, $value) {
    $existing = fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
    
    if ($existing) {
        return updateData('site_settings', ['setting_value' => $value], 'setting_key = ?', ['setting_key' => $key]);
    } else {
        return insertData('site_settings', ['setting_key' => $key, 'setting_value' => $value]);
    }
}

/**
 * إرسال بريد إلكتروني
 */
function sendEmail($to, $subject, $message, $from = null) {
    if (!$from) {
        $from = getSetting('contact_email', '<EMAIL>');
    }
    
    $headers = "From: $from\r\n";
    $headers .= "Reply-To: $from\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}

/**
 * تسجيل نشاط في السجل
 */
function logActivity($action, $details = '') {
    $adminId = $_SESSION['admin_id'] ?? 0;
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    
    insertData('activity_log', [
        'admin_id' => $adminId,
        'action' => $action,
        'details' => $details,
        'ip_address' => $ip
    ]);
}

/**
 * تنظيف النص من HTML
 */
function stripHtmlTags($text, $allowedTags = '') {
    return strip_tags($text, $allowedTags);
}

/**
 * اختصار النص
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}
?>
