    <!-- Footer -->
    <footer class="bg-dark text-light mt-5">
        <div class="container py-5">
            <div class="row">
                <!-- معلومات الموقع -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3">
                        <i class="bi bi-shop"></i> <?php echo getSetting('site_name'); ?>
                    </h5>
                    <p class="text-muted">
                        <?php echo getSetting('site_description'); ?>
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="bi bi-instagram"></i></a>
                        <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>" 
                           class="text-light me-3" target="_blank">
                            <i class="bi bi-whatsapp"></i>
                        </a>
                    </div>
                </div>
                
                <!-- روابط سريعة -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo SITE_URL; ?>" class="text-muted text-decoration-none">الرئيسية</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/products.php" class="text-muted text-decoration-none">المنتجات</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/offers.php" class="text-muted text-decoration-none">العروض</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/contact.php" class="text-muted text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <!-- التصنيفات -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">التصنيفات</h6>
                    <ul class="list-unstyled">
                        <?php
                        $categories = fetchAll("SELECT id, name FROM categories ORDER BY name LIMIT 5");
                        if ($categories):
                            foreach ($categories as $category):
                        ?>
                            <li>
                                <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $category['id']; ?>" 
                                   class="text-muted text-decoration-none">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                            </li>
                        <?php 
                            endforeach;
                        endif; 
                        ?>
                    </ul>
                </div>
                
                <!-- معلومات الاتصال -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h6 class="mb-3">تواصل معنا</h6>
                    <div class="contact-info">
                        <p class="mb-2">
                            <i class="bi bi-telephone"></i>
                            <a href="tel:<?php echo getSetting('contact_phone'); ?>" class="text-muted text-decoration-none">
                                <?php echo getSetting('contact_phone'); ?>
                            </a>
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-envelope"></i>
                            <a href="mailto:<?php echo getSetting('contact_email'); ?>" class="text-muted text-decoration-none">
                                <?php echo getSetting('contact_email'); ?>
                            </a>
                        </p>
                        <p class="mb-3">
                            <i class="bi bi-whatsapp"></i>
                            <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>" 
                               class="text-muted text-decoration-none" target="_blank">
                                واتساب: <?php echo getSetting('whatsapp_number'); ?>
                            </a>
                        </p>
                    </div>
                    
                    <!-- النشرة البريدية -->
                    <div class="newsletter">
                        <h6 class="mb-2">اشترك في النشرة البريدية</h6>
                        <form id="newsletterForm" class="d-flex">
                            <input type="email" class="form-control me-2" placeholder="بريدك الإلكتروني" 
                                   name="email" required>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send"></i>
                            </button>
                        </form>
                        <small class="text-muted">احصل على آخر العروض والمنتجات الجديدة</small>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <!-- معلومات إضافية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <i class="bi bi-truck text-primary me-2"></i>
                        <span>توصيل مجاني للطلبات أكثر من <?php echo formatPrice(getSetting('free_delivery_threshold')); ?></span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <i class="bi bi-shield-check text-success me-2"></i>
                        <span>ضمان الجودة والأصالة</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-headset text-info me-2"></i>
                        <span>دعم فني على مدار الساعة</span>
                    </div>
                </div>
                
                <div class="col-md-6 text-md-end">
                    <div class="payment-methods">
                        <span class="text-muted">طرق الدفع المتاحة:</span>
                        <div class="mt-2">
                            <i class="bi bi-cash text-success fs-4 me-2" title="الدفع عند الاستلام"></i>
                            <i class="bi bi-bank text-primary fs-4 me-2" title="التحويل البنكي"></i>
                            <i class="bi bi-credit-card text-warning fs-4" title="البطاقات الائتمانية"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Copyright -->
        <div class="bg-secondary py-3">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0 text-muted">
                            &copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name'); ?>. 
                            جميع الحقوق محفوظة.
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="#" class="text-muted text-decoration-none me-3">سياسة الخصوصية</a>
                        <a href="#" class="text-muted text-decoration-none me-3">الشروط والأحكام</a>
                        <a href="#" class="text-muted text-decoration-none">سياسة الإرجاع</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // النشرة البريدية
        document.getElementById('newsletterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('<?php echo SITE_URL; ?>/ajax/newsletter.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم الاشتراك بنجاح في النشرة البريدية!');
                    this.reset();
                } else {
                    alert(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ، يرجى المحاولة مرة أخرى');
            });
        });

        // إضافة منتج إلى السلة
        function addToCart(productId, quantity = 1) {
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', quantity);
            
            fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث عداد السلة
                    updateCartCount();
                    
                    // إظهار رسالة نجاح
                    showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');
                } else {
                    showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
            });
        }

        // تحديث عداد السلة
        function updateCartCount() {
            fetch('<?php echo SITE_URL; ?>/ajax/cart_count.php')
            .then(response => response.json())
            .then(data => {
                const cartBadge = document.querySelector('.cart-badge');
                if (data.count > 0) {
                    if (cartBadge) {
                        cartBadge.textContent = data.count;
                    } else {
                        const cartIcon = document.querySelector('.cart-icon');
                        const badge = document.createElement('span');
                        badge.className = 'cart-badge';
                        badge.textContent = data.count;
                        cartIcon.appendChild(badge);
                    }
                } else if (cartBadge) {
                    cartBadge.remove();
                }
            });
        }

        // إظهار رسائل Toast
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();
            
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }

        // العودة إلى الأعلى
        window.addEventListener('scroll', function() {
            const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            const backToTop = document.getElementById('backToTop');
            
            if (scrollTop > 300) {
                if (!backToTop) {
                    createBackToTopButton();
                }
            } else if (backToTop) {
                backToTop.remove();
            }
        });

        function createBackToTopButton() {
            const button = document.createElement('button');
            button.id = 'backToTop';
            button.className = 'btn btn-primary position-fixed';
            button.style.cssText = 'bottom: 20px; left: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;';
            button.innerHTML = '<i class="bi bi-arrow-up"></i>';
            button.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
            document.body.appendChild(button);
        }
    </script>
</body>
</html>
