<?php
$pageTitle = 'تم تأكيد الطلب';
require_once 'includes/header.php';

// التحقق من وجود رقم الطلب
if (!isset($_GET['order']) || !is_numeric($_GET['order'])) {
    header('Location: ' . SITE_URL);
    exit();
}

$orderId = (int)$_GET['order'];

// جلب تفاصيل الطلب
$order = fetchOne("
    SELECT * FROM orders 
    WHERE id = ?
", [$orderId]);

if (!$order) {
    header('Location: ' . SITE_URL);
    exit();
}

// جلب عناصر الطلب
$orderItems = fetchAll("
    SELECT oi.*, p.image 
    FROM order_items oi 
    LEFT JOIN products p ON oi.product_id = p.id 
    WHERE oi.order_id = ?
", [$orderId]);
?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="text-center mb-5">
                <div class="success-icon mb-4">
                    <i class="bi bi-check-circle-fill text-success" style="font-size: 5rem;"></i>
                </div>
                <h1 class="text-success mb-3">تم تأكيد طلبك بنجاح!</h1>
                <p class="lead text-muted">
                    شكراً لك على ثقتك بنا. تم استلام طلبك وسيتم معالجته في أقرب وقت ممكن.
                </p>
                <div class="alert alert-success d-inline-block">
                    <strong>رقم الطلب: #<?php echo $order['id']; ?></strong>
                </div>
            </div>
            
            <!-- Order Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-receipt"></i> تفاصيل الطلب
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معلومات العميل:</h6>
                            <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($order['customer_name']); ?></p>
                            <p class="mb-1"><strong>الهاتف:</strong> <?php echo htmlspecialchars($order['customer_phone']); ?></p>
                            <?php if ($order['customer_email']): ?>
                                <p class="mb-1"><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($order['customer_email']); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <h6>معلومات الطلب:</h6>
                            <p class="mb-1"><strong>تاريخ الطلب:</strong> <?php echo formatDate($order['created_at']); ?></p>
                            <p class="mb-1"><strong>حالة الطلب:</strong> 
                                <span class="badge bg-warning">قيد المعالجة</span>
                            </p>
                            <p class="mb-1"><strong>طريقة الدفع:</strong> 
                                <?php echo $order['payment_method'] == 'cash_on_delivery' ? 'الدفع عند الاستلام' : 'التحويل البنكي'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Shipping Address -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt"></i> عنوان التوصيل
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-1"><?php echo nl2br(htmlspecialchars($order['address'])); ?></p>
                    <p class="mb-1"><?php echo htmlspecialchars($order['province']); ?><?php echo $order['city'] ? ', ' . htmlspecialchars($order['city']) : ''; ?></p>
                    <?php if ($order['postal_code']): ?>
                        <p class="mb-0">الرمز البريدي: <?php echo htmlspecialchars($order['postal_code']); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bag"></i> المنتجات المطلوبة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo $item['image'] ? UPLOAD_URL . '/' . $item['image'] : 'https://via.placeholder.com/60x60/f8f9fa/6c757d?text=صورة'; ?>" 
                                                     alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                                     class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                <span><?php echo htmlspecialchars($item['product_name']); ?></span>
                                            </div>
                                        </td>
                                        <td class="align-middle"><?php echo formatPrice($item['price']); ?></td>
                                        <td class="align-middle"><?php echo $item['quantity']; ?></td>
                                        <td class="align-middle fw-bold"><?php echo formatPrice($item['total']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calculator"></i> ملخص الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <div class="order-summary">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span><?php echo formatPrice($order['subtotal']); ?></span>
                                </div>
                                
                                <?php if ($order['discount_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2 text-success">
                                        <span>الخصم:</span>
                                        <span>-<?php echo formatPrice($order['discount_amount']); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span>التوصيل:</span>
                                    <span>
                                        <?php if ($order['delivery_price'] == 0): ?>
                                            <span class="text-success">مجاني</span>
                                        <?php else: ?>
                                            <?php echo formatPrice($order['delivery_price']); ?>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>الإجمالي:</strong>
                                    <strong class="text-primary fs-5"><?php echo formatPrice($order['total_price']); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Notes -->
            <?php if ($order['notes']): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-text"></i> ملاحظات الطلب
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Next Steps -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> الخطوات التالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <span class="text-white fw-bold">1</span>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <h6>مراجعة الطلب</h6>
                                    <p class="small text-muted mb-0">سيتم مراجعة طلبك والتأكد من توفر المنتجات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <span class="text-white fw-bold">2</span>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <h6>تأكيد الطلب</h6>
                                    <p class="small text-muted mb-0">سنتصل بك لتأكيد تفاصيل الطلب والتوصيل</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <span class="text-white fw-bold">3</span>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <h6>تحضير الطلب</h6>
                                    <p class="small text-muted mb-0">سيتم تحضير وتغليف طلبك بعناية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <span class="text-white fw-bold">4</span>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <h6>التوصيل</h6>
                                    <p class="small text-muted mb-0">سيتم توصيل طلبك إلى العنوان المحدد</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="text-center">
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                        <i class="bi bi-grid"></i> متابعة التسوق
                    </a>
                    <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>?text=استفسار عن الطلب رقم <?php echo $order['id']; ?>" 
                       class="btn btn-success" target="_blank">
                        <i class="bi bi-whatsapp"></i> تواصل معنا
                    </a>
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i> طباعة الطلب
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.success-icon {
    animation: bounceIn 1s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }
.card:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@media print {
    .btn, .navbar, .footer {
        display: none !important;
    }

    .container {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }

    .success-icon {
        display: none !important;
    }
}
</style>

<script>
// تأثيرات بصرية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير للأيقونة
    const successIcon = document.querySelector('.success-icon i');
    if (successIcon) {
        setTimeout(() => {
            successIcon.style.transform = 'scale(1.1)';
            setTimeout(() => {
                successIcon.style.transform = 'scale(1)';
            }, 200);
        }, 1000);
    }

    // إضافة تأثير للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });
});

// نسخ رقم الطلب
function copyOrderNumber() {
    const orderNumber = '<?php echo $order['id']; ?>';
    navigator.clipboard.writeText(orderNumber).then(() => {
        showToast('تم نسخ رقم الطلب', 'success');
    });
}

// إضافة حدث النقر على رقم الطلب
document.addEventListener('DOMContentLoaded', function() {
    const orderAlert = document.querySelector('.alert-success');
    if (orderAlert) {
        orderAlert.style.cursor = 'pointer';
        orderAlert.title = 'انقر لنسخ رقم الطلب';
        orderAlert.addEventListener('click', copyOrderNumber);
    }
});

// تحسين تجربة الطباعة
window.addEventListener('beforeprint', function() {
    document.title = 'طلب رقم <?php echo $order['id']; ?> - <?php echo getSetting('site_name'); ?>';
});

// إضافة معلومات إضافية للطباعة
window.addEventListener('beforeprint', function() {
    const printInfo = document.createElement('div');
    printInfo.className = 'print-only';
    printInfo.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px;">
            <h2><?php echo getSetting('site_name'); ?></h2>
            <p>الهاتف: <?php echo getSetting('contact_phone'); ?> | البريد الإلكتروني: <?php echo getSetting('contact_email'); ?></p>
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
    `;

    document.body.insertBefore(printInfo, document.body.firstChild);
});

window.addEventListener('afterprint', function() {
    const printInfo = document.querySelector('.print-only');
    if (printInfo) {
        printInfo.remove();
    }
});

// إرسال إشعار بالبريد الإلكتروني (إذا كان متوفراً)
<?php if ($order['customer_email']): ?>
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة كود لإرسال إشعار بالبريد الإلكتروني
    console.log('سيتم إرسال إشعار بالبريد الإلكتروني إلى: <?php echo $order['customer_email']; ?>');
});
<?php endif; ?>

// تتبع الطلب (يمكن تطويره لاحقاً)
function trackOrder() {
    showToast('ميزة تتبع الطلب ستكون متاحة قريباً', 'info');
}

// مشاركة تفاصيل الطلب
function shareOrder() {
    if (navigator.share) {
        navigator.share({
            title: 'طلب رقم <?php echo $order['id']; ?>',
            text: 'تم تأكيد طلبي من <?php echo getSetting('site_name'); ?>',
            url: window.location.href
        });
    } else {
        copyOrderNumber();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
