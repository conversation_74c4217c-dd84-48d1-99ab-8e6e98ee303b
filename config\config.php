<?php
/**
 * ملف الإعدادات العامة للموقع
 * General Site Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/database.php';

// إعدادات الموقع الأساسية
define('SITE_URL', 'http://localhost/web');
define('SITE_PATH', __DIR__ . '/..');
define('UPLOAD_PATH', SITE_PATH . '/uploads');
define('UPLOAD_URL', SITE_URL . '/uploads');

// إعدادات الأمان
define('ADMIN_SESSION_NAME', 'admin_logged_in');
define('CART_SESSION_NAME', 'shopping_cart');

// إعدادات الصور
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

/**
 * دالة جلب إعدادات الموقع من قاعدة البيانات
 */
function getSiteSettings() {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        $result = fetchAll("SELECT setting_key, setting_value FROM site_settings");
        
        if ($result) {
            foreach ($result as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }
        
        // إعدادات افتراضية في حالة عدم وجود بيانات
        $defaults = [
            'site_name' => 'متجري الإلكتروني',
            'site_description' => 'متجر إلكتروني شامل',
            'delivery_price' => '25.00',
            'free_delivery_threshold' => '200.00',
            'contact_phone' => '+964501234567',
            'contact_email' => '<EMAIL>',
            'whatsapp_number' => '+964501234567',
            'currency' => 'دينار عراقي'
        ];
        
        foreach ($defaults as $key => $value) {
            if (!isset($settings[$key])) {
                $settings[$key] = $value;
            }
        }
    }
    
    return $settings;
}

/**
 * دالة جلب إعداد محدد
 */
function getSetting($key, $default = '') {
    $settings = getSiteSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * دالة تحديث إعداد
 */
function updateSetting($key, $value) {
    $existing = fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
    
    if ($existing) {
        return updateData('site_settings', ['setting_value' => $value], 'setting_key = ?', [$key]);
    } else {
        return insertData('site_settings', [
            'setting_key' => $key,
            'setting_value' => $value
        ]);
    }
}

/**
 * دالة التحقق من تسجيل دخول المدير
 */
function isAdminLoggedIn() {
    return isset($_SESSION[ADMIN_SESSION_NAME]) && $_SESSION[ADMIN_SESSION_NAME] === true;
}

/**
 * دالة إعادة توجيه المدير إلى صفحة الدخول
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: ' . SITE_URL . '/admin/login.php');
        exit();
    }
}

/**
 * دالة تسجيل خروج المدير
 */
function adminLogout() {
    unset($_SESSION[ADMIN_SESSION_NAME]);
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_username']);
}

/**
 * دالة جلب سلة التسوق
 */
function getCart() {
    return isset($_SESSION[CART_SESSION_NAME]) ? $_SESSION[CART_SESSION_NAME] : [];
}

/**
 * دالة إضافة منتج إلى السلة
 */
function addToCart($productId, $quantity = 1) {
    $cart = getCart();
    
    if (isset($cart[$productId])) {
        $cart[$productId] += $quantity;
    } else {
        $cart[$productId] = $quantity;
    }
    
    $_SESSION[CART_SESSION_NAME] = $cart;
    return true;
}

/**
 * دالة تحديث كمية منتج في السلة
 */
function updateCartItem($productId, $quantity) {
    $cart = getCart();
    
    if ($quantity <= 0) {
        unset($cart[$productId]);
    } else {
        $cart[$productId] = $quantity;
    }
    
    $_SESSION[CART_SESSION_NAME] = $cart;
    return true;
}

/**
 * دالة حذف منتج من السلة
 */
function removeFromCart($productId) {
    $cart = getCart();
    unset($cart[$productId]);
    $_SESSION[CART_SESSION_NAME] = $cart;
    return true;
}

/**
 * دالة إفراغ السلة
 */
function clearCart() {
    unset($_SESSION[CART_SESSION_NAME]);
    return true;
}

/**
 * دالة حساب إجمالي السلة
 */
function getCartTotal() {
    $cart = getCart();
    $total = 0;
    
    if (!empty($cart)) {
        $productIds = array_keys($cart);
        $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
        
        $products = fetchAll("SELECT id, price, discount FROM products WHERE id IN ($placeholders)", $productIds);
        
        foreach ($products as $product) {
            $quantity = $cart[$product['id']];
            $price = $product['price'];
            
            // تطبيق الخصم إن وجد
            if ($product['discount'] > 0) {
                $price = $price - ($price * $product['discount'] / 100);
            }
            
            $total += $price * $quantity;
        }
    }
    
    return $total;
}

/**
 * دالة عدد عناصر السلة
 */
function getCartItemCount() {
    $cart = getCart();
    return array_sum($cart);
}

/**
 * دالة تنسيق السعر - محدثة لعرض أرقام صحيحة بدون فواصل عشرية
 */
function formatPrice($price) {
    // تحويل السعر إلى رقم صحيح (إزالة الفواصل العشرية)
    $wholePrice = (int)round($price);
    // تنسيق الرقم مع فواصل الآلاف وعرض العملة العراقية
    return number_format($wholePrice, 0, '.', ',') . ' دينار عراقي';
}

/**
 * دالة تنسيق التاريخ
 */
function formatDate($date) {
    return date('Y-m-d H:i', strtotime($date));
}

/**
 * دالة رفع الصور
 */
function uploadImage($file, $folder = 'products') {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }
    
    $uploadDir = UPLOAD_PATH . '/' . $folder . '/';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // التحقق من نوع الملف
    if (!in_array($fileExtension, ALLOWED_IMAGE_TYPES)) {
        return false;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    // إنشاء اسم فريد للملف
    $fileName = uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return $folder . '/' . $fileName;
    }
    
    return false;
}

/**
 * دالة حذف الصورة
 */
function deleteImage($imagePath) {
    $fullPath = UPLOAD_PATH . '/' . $imagePath;
    if (file_exists($fullPath)) {
        return unlink($fullPath);
    }
    return false;
}

/**
 * دالة عرض الرسائل
 */
function showMessage($message, $type = 'info') {
    $alertClass = '';
    switch ($type) {
        case 'success':
            $alertClass = 'alert-success';
            break;
        case 'error':
            $alertClass = 'alert-danger';
            break;
        case 'warning':
            $alertClass = 'alert-warning';
            break;
        default:
            $alertClass = 'alert-info';
    }
    
    return "<div class='alert {$alertClass} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}
?>
